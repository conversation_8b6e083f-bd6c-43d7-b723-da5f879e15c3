using System;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;
using ToolsService;
using System.ComponentModel.DataAnnotations.Schema;
using GameServer.ORM;
using GameServer.ExcelData;


namespace ExcelToData
{

/// <summary>
/// Account
/// </summary>
[Table("accounts")]
public class Account
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// 服务器id
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 平台id
	/// </summary>
	public uint platformID
	{
		get;set;
	}

	/// <summary>
	/// 用户角色uuids
	/// </summary>
	public string uuids
	{
		get;set;
	}

	/// <summary>
	/// 平台名称
	/// </summary>
	public string platName
	{
		get;set;
	}

	/// <summary>
	/// 角色编号
	/// </summary>
	public int roleID
	{
		get;set;
	}

	/// <summary>
	/// 角色数量
	/// </summary>
	public int roleSum
	{
		get;set;
	}

	/// <summary>
	/// 形象id
	/// </summary>
	public string avatar
	{
		get;set;
	}

	/// <summary>
	/// 真实名字
	/// </summary>
	public string real_name
	{
		get;set;
	}

	/// <summary>
	/// 昵称/显示名
	/// </summary>
	public string nickname
	{
		get;set;
	}

	/// <summary>
	/// 用户名字
	/// </summary>
	public string usrname
	{
		get;set;
	}

	/// <summary>
	/// 用户密码
	/// </summary>
	public string password
	{
		get;set;
	}

	/// <summary>
	/// 手机号码
	/// </summary>
	public string mobile
	{
		get;set;
	}

	/// <summary>
	/// 邮箱地址-google
	/// </summary>
	public string email
	{
		get;set;
	}

	/// <summary>
	/// 生日时间
	/// </summary>
	public long birth_date
	{
		get;set;
	}

	/// <summary>
	/// 性别
	/// </summary>
	public byte gender
	{
		get;set;
	}

	/// <summary>
	/// 安全问题
	/// </summary>
	public string[] security_question
	{
		get;set;
	}

	/// <summary>
	/// 安全答案
	/// </summary>
	public string[] security_answer
	{
		get;set;
	}

	/// <summary>
	/// 二次验证:0-关闭 1-开启
	/// </summary>
	public bool two_factor_auth
	{
		get;set;
	}

	/// <summary>
	/// 语言偏好
	/// </summary>
	public string language
	{
		get;set;
	}

	/// <summary>
	/// 时区
	/// </summary>
	public string timezone
	{
		get;set;
	}

	/// <summary>
	/// 信任的设备唯一码
	/// </summary>
	public string[] device
	{
		get;set;
	}

	/// <summary>
	/// 删除时间
	/// </summary>
	public long delete_time
	{
		get;set;
	}

	/// <summary>
	/// 是否删除
	/// </summary>
	public bool deleted
	{
		get;set;
	}

	/// <summary>
	/// ip
	/// </summary>
	public string ip
	{
		get;set;
	}

	/// <summary>
	/// 城市
	/// </summary>
	public string city
	{
		get;set;
	}

	/// <summary>
	/// 账户状态
	/// </summary>
	public byte state
	{
		get;set;
	}

	/// <summary>
	/// 上一次登录ip
	/// </summary>
	public string lastIP
	{
		get;set;
	}

	/// <summary>
	/// 上一次登录时间
	/// </summary>
	public string lastLogin
	{
		get;set;
	}

	/// <summary>
	/// 本次登录时间
	/// </summary>
	public string currentLogin
	{
		get;set;
	}

	/// <summary>
	/// 用户id
	/// </summary>
	public string userId
	{
		get;set;
	}

	/// <summary>
	/// 显示的名称
	/// </summary>
	public string displayName
	{
		get;set;
	}

	/// <summary>
	/// 姓
	/// </summary>
	public string familyName
	{
		get;set;
	}

	/// <summary>
	/// 名字
	/// </summary>
	public string givenName
	{
		get;set;
	}

	/// <summary>
	/// 头像地址
	/// </summary>
	public string imageUrl
	{
		get;set;
	}

	/// <summary>
	/// idToken
	/// </summary>
	public string idToken
	{
		get;set;
	}

	/// <summary>
	/// serverAuthCode
	/// </summary>
	public string serverAuthCode
	{
		get;set;
	}

	/// <summary>
	/// OAuth2
	/// </summary>
	public string accessToken
	{
		get;set;
	}

	/// <summary>
	/// 金币
	/// </summary>
	public int money
	{
		get;set;
	}

	/// <summary>
	/// 钻石
	/// </summary>
	public int diamond
	{
		get;set;
	}

	/// <summary>
	/// 游戏天数
	/// </summary>
	public int playDays
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/Account.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			platformID = (uint)Convert.ChangeType(raw_data[i][1].ToString(), typeof(uint));
		if(raw_data[i][2].ToString() != "")
			uuids = (string)Convert.ChangeType(raw_data[i][2].ToString(), typeof(string));
		if(raw_data[i][3].ToString() != "")
			platName = (string)Convert.ChangeType(raw_data[i][3].ToString(), typeof(string));
		if(raw_data[i][4].ToString() != "")
			roleID = (int)Convert.ChangeType(raw_data[i][4].ToString(), typeof(int));
		if(raw_data[i][5].ToString() != "")
			roleSum = (int)Convert.ChangeType(raw_data[i][5].ToString(), typeof(int));
		if(raw_data[i][6].ToString() != "")
			avatar = (string)Convert.ChangeType(raw_data[i][6].ToString(), typeof(string));
		if(raw_data[i][7].ToString() != "")
			real_name = (string)Convert.ChangeType(raw_data[i][7].ToString(), typeof(string));
		if(raw_data[i][8].ToString() != "")
			nickname = (string)Convert.ChangeType(raw_data[i][8].ToString(), typeof(string));
		if(raw_data[i][9].ToString() != "")
			usrname = (string)Convert.ChangeType(raw_data[i][9].ToString(), typeof(string));
		if(raw_data[i][10].ToString() != "")
			password = (string)Convert.ChangeType(raw_data[i][10].ToString(), typeof(string));
		if(raw_data[i][11].ToString() != "")
			mobile = (string)Convert.ChangeType(raw_data[i][11].ToString(), typeof(string));
		if(raw_data[i][12].ToString() != "")
			email = (string)Convert.ChangeType(raw_data[i][12].ToString(), typeof(string));
		if(raw_data[i][13].ToString() != "")
			birth_date = (long)Convert.ChangeType(raw_data[i][13].ToString(), typeof(long));
		if(raw_data[i][14].ToString() != "")
			gender = (byte)Convert.ChangeType(raw_data[i][14].ToString(), typeof(byte));
		if(raw_data[i][15].ToString() != "")
			security_question = (string[])Convert.ChangeType(raw_data[i][15].ToString(), typeof(string[]));
		if(raw_data[i][16].ToString() != "")
			security_answer = (string[])Convert.ChangeType(raw_data[i][16].ToString(), typeof(string[]));
		if(raw_data[i][17].ToString() != "")
			two_factor_auth = (bool)Convert.ChangeType(raw_data[i][17].ToString(), typeof(bool));
		if(raw_data[i][18].ToString() != "")
			language = (string)Convert.ChangeType(raw_data[i][18].ToString(), typeof(string));
		if(raw_data[i][19].ToString() != "")
			timezone = (string)Convert.ChangeType(raw_data[i][19].ToString(), typeof(string));
		if(raw_data[i][20].ToString() != "")
			device = (string[])Convert.ChangeType(raw_data[i][20].ToString(), typeof(string[]));
		if(raw_data[i][21].ToString() != "")
			delete_time = (long)Convert.ChangeType(raw_data[i][21].ToString(), typeof(long));
		if(raw_data[i][22].ToString() != "")
			deleted = (bool)Convert.ChangeType(raw_data[i][22].ToString(), typeof(bool));
		if(raw_data[i][23].ToString() != "")
			ip = (string)Convert.ChangeType(raw_data[i][23].ToString(), typeof(string));
		if(raw_data[i][24].ToString() != "")
			city = (string)Convert.ChangeType(raw_data[i][24].ToString(), typeof(string));
		if(raw_data[i][25].ToString() != "")
			state = (byte)Convert.ChangeType(raw_data[i][25].ToString(), typeof(byte));
		if(raw_data[i][26].ToString() != "")
			lastIP = (string)Convert.ChangeType(raw_data[i][26].ToString(), typeof(string));
		if(raw_data[i][27].ToString() != "")
			lastLogin = (string)Convert.ChangeType(raw_data[i][27].ToString(), typeof(string));
		if(raw_data[i][28].ToString() != "")
			currentLogin = (string)Convert.ChangeType(raw_data[i][28].ToString(), typeof(string));
		if(raw_data[i][29].ToString() != "")
			userId = (string)Convert.ChangeType(raw_data[i][29].ToString(), typeof(string));
		if(raw_data[i][30].ToString() != "")
			displayName = (string)Convert.ChangeType(raw_data[i][30].ToString(), typeof(string));
		if(raw_data[i][31].ToString() != "")
			familyName = (string)Convert.ChangeType(raw_data[i][31].ToString(), typeof(string));
		if(raw_data[i][32].ToString() != "")
			givenName = (string)Convert.ChangeType(raw_data[i][32].ToString(), typeof(string));
		if(raw_data[i][33].ToString() != "")
			imageUrl = (string)Convert.ChangeType(raw_data[i][33].ToString(), typeof(string));
		if(raw_data[i][34].ToString() != "")
			idToken = (string)Convert.ChangeType(raw_data[i][34].ToString(), typeof(string));
		if(raw_data[i][35].ToString() != "")
			serverAuthCode = (string)Convert.ChangeType(raw_data[i][35].ToString(), typeof(string));
		if(raw_data[i][36].ToString() != "")
			accessToken = (string)Convert.ChangeType(raw_data[i][36].ToString(), typeof(string));
		if(raw_data[i][37].ToString() != "")
			money = (int)Convert.ChangeType(raw_data[i][37].ToString(), typeof(int));
		if(raw_data[i][38].ToString() != "")
			diamond = (int)Convert.ChangeType(raw_data[i][38].ToString(), typeof(int));
		if(raw_data[i][39].ToString() != "")
			playDays = (int)Convert.ChangeType(raw_data[i][39].ToString(), typeof(int));
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"platformID", "uint"},
		{"uuids", "string"},
		{"platName", "string"},
		{"roleID", "int"},
		{"roleSum", "int"},
		{"avatar", "string"},
		{"real_name", "string"},
		{"nickname", "string"},
		{"usrname", "string"},
		{"password", "string"},
		{"mobile", "string"},
		{"email", "string"},
		{"birth_date", "long"},
		{"gender", "byte"},
		{"security_question", "string[]"},
		{"security_answer", "string[]"},
		{"two_factor_auth", "bool"},
		{"language", "string"},
		{"timezone", "string"},
		{"device", "string[]"},
		{"delete_time", "long"},
		{"deleted", "bool"},
		{"ip", "string"},
		{"city", "string"},
		{"state", "byte"},
		{"lastIP", "string"},
		{"lastLogin", "string"},
		{"currentLogin", "string"},
		{"userId", "string"},
		{"displayName", "string"},
		{"familyName", "string"},
		{"givenName", "string"},
		{"imageUrl", "string"},
		{"idToken", "string"},
		{"serverAuthCode", "string"},
		{"accessToken", "string"},
		{"money", "int"},
		{"diamond", "int"},
		{"playDays", "int"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "platformID=" + platformID + "uuids=" + uuids + "platName=" + platName + "roleID=" + roleID + "roleSum=" + roleSum + "avatar=" + avatar + "real_name=" + real_name + "nickname=" + nickname + "usrname=" + usrname + "password=" + password + "mobile=" + mobile + "email=" + email + "birth_date=" + birth_date + "gender=" + gender + "security_question=" + security_question + "security_answer=" + security_answer + "two_factor_auth=" + two_factor_auth + "language=" + language + "timezone=" + timezone + "device=" + device + "delete_time=" + delete_time + "deleted=" + deleted + "ip=" + ip + "city=" + city + "state=" + state + "lastIP=" + lastIP + "lastLogin=" + lastLogin + "currentLogin=" + currentLogin + "userId=" + userId + "displayName=" + displayName + "familyName=" + familyName + "givenName=" + givenName + "imageUrl=" + imageUrl + "idToken=" + idToken + "serverAuthCode=" + serverAuthCode + "accessToken=" + accessToken + "money=" + money + "diamond=" + diamond + "playDays=" + playDays;
	}
	public static string[] propList = { "id","platformID","uuids","platName","roleID","roleSum","avatar","real_name","nickname","usrname","password","mobile","email","birth_date","gender","security_question","security_answer","two_factor_auth","language","timezone","device","delete_time","deleted","ip","city","state","lastIP","lastLogin","currentLogin","userId","displayName","familyName","givenName","imageUrl","idToken","serverAuthCode","accessToken","money","diamond","playDays", };
	public static Account add(Account a, Account b, uint start, uint end, Account limit = null) {
		if(a == null || b == null) return null;
		Account result = new Account();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static Account sub(Account a, Account b, uint start, uint end) {
		if(a == null || b == null) return null;
		Account result = new Account();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(Account a, Account b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static Account max(Account a, Account b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static Account json(Account a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static Account setProperty(Account a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<Account> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/Account.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("Account的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<Account> list = new List<Account>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			Account obj = new Account();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.platformID= br.ReadUInt32();
			obj.uuids= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.platName= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.roleID= br.ReadInt32();
			obj.roleSum= br.ReadInt32();
			obj.avatar= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.real_name= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.nickname= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.usrname= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.password= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.mobile= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.email= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.birth_date= br.ReadInt64();
			obj.gender= br.ReadByte();
			obj.security_question= new string[1];
			int security_questionNum=br.ReadInt32();
			for (int tmp = 0; tmp < security_questionNum; tmp++){
				string tmpStr = System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16())) + (tmp < security_questionNum - 1 ? "," : "");
				obj.security_question[0]+=tmpStr;
			}
			obj.security_answer= new string[1];
			int security_answerNum=br.ReadInt32();
			for (int tmp = 0; tmp < security_answerNum; tmp++){
				string tmpStr = System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16())) + (tmp < security_answerNum - 1 ? "," : "");
				obj.security_answer[0]+=tmpStr;
			}
			obj.two_factor_auth= br.ReadBoolean();
			obj.language= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.timezone= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.device= new string[1];
			int deviceNum=br.ReadInt32();
			for (int tmp = 0; tmp < deviceNum; tmp++){
				string tmpStr = System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16())) + (tmp < deviceNum - 1 ? "," : "");
				obj.device[0]+=tmpStr;
			}
			obj.delete_time= br.ReadInt64();
			obj.deleted= br.ReadBoolean();
			obj.ip= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.city= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.state= br.ReadByte();
			obj.lastIP= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.lastLogin= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.currentLogin= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.userId= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.displayName= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.familyName= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.givenName= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.imageUrl= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.idToken= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.serverAuthCode= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.accessToken= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.money= br.ReadInt32();
			obj.diamond= br.ReadInt32();
			obj.playDays= br.ReadInt32();
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}
}


/// <summary>
/// AI_Game_Output
/// </summary>
[Table("ai_game_outputs")]
public class AI_Game_Output
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// id
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 设置前提
	/// </summary>
	public string systemPrompt
	{
		get;set;
	}

	/// <summary>
	/// 生成模板
	/// </summary>
	public string generateTemplate
	{
		get;set;
	}

	/// <summary>
	/// 模板描述
	/// </summary>
	public string templateDescription
	{
		get;set;
	}

	/// <summary>
	/// 是否输出markdown
	/// </summary>
	public bool ifMarkdown
	{
		get;set;
	}

	/// <summary>
	/// json输出模板
	/// </summary>
	public string jsonModle
	{
		get;set;
	}

	/// <summary>
	/// 是否输出json
	/// </summary>
	public bool ifJson
	{
		get;set;
	}

	/// <summary>
	/// 是否以Sse输出展示
	/// </summary>
	public bool ifSse
	{
		get;set;
	}

	/// <summary>
	/// 输出步骤
	/// </summary>
	public int step
	{
		get;set;
	}

	/// <summary>
	/// 是否可以同组并行
	/// </summary>
	public bool ifParallel
	{
		get;set;
	}

	/// <summary>
	/// 生成优先级
	/// </summary>
	public string priority
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/AI_Game_Output.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			systemPrompt = (string)Convert.ChangeType(raw_data[i][1].ToString(), typeof(string));
		if(raw_data[i][2].ToString() != "")
			generateTemplate = (string)Convert.ChangeType(raw_data[i][2].ToString(), typeof(string));
		if(raw_data[i][3].ToString() != "")
			templateDescription = (string)Convert.ChangeType(raw_data[i][3].ToString(), typeof(string));
		if(raw_data[i][4].ToString() != "")
			ifMarkdown = (bool)Convert.ChangeType(raw_data[i][4].ToString(), typeof(bool));
		if(raw_data[i][5].ToString() != "")
			jsonModle = (string)Convert.ChangeType(raw_data[i][5].ToString(), typeof(string));
		if(raw_data[i][6].ToString() != "")
			ifJson = (bool)Convert.ChangeType(raw_data[i][6].ToString(), typeof(bool));
		if(raw_data[i][7].ToString() != "")
			ifSse = (bool)Convert.ChangeType(raw_data[i][7].ToString(), typeof(bool));
		if(raw_data[i][8].ToString() != "")
			step = (int)Convert.ChangeType(raw_data[i][8].ToString(), typeof(int));
		if(raw_data[i][9].ToString() != "")
			ifParallel = (bool)Convert.ChangeType(raw_data[i][9].ToString(), typeof(bool));
		if(raw_data[i][10].ToString() != "")
			priority = (string)Convert.ChangeType(raw_data[i][10].ToString(), typeof(string));
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"systemPrompt", "string"},
		{"generateTemplate", "string"},
		{"templateDescription", "string"},
		{"ifMarkdown", "bool"},
		{"jsonModle", "string"},
		{"ifJson", "bool"},
		{"ifSse", "bool"},
		{"step", "int"},
		{"ifParallel", "bool"},
		{"priority", "string"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "systemPrompt=" + systemPrompt + "generateTemplate=" + generateTemplate + "templateDescription=" + templateDescription + "ifMarkdown=" + ifMarkdown + "jsonModle=" + jsonModle + "ifJson=" + ifJson + "ifSse=" + ifSse + "step=" + step + "ifParallel=" + ifParallel + "priority=" + priority;
	}
	public static string[] propList = { "id","systemPrompt","generateTemplate","templateDescription","ifMarkdown","jsonModle","ifJson","ifSse","step","ifParallel","priority", };
	public static AI_Game_Output add(AI_Game_Output a, AI_Game_Output b, uint start, uint end, AI_Game_Output limit = null) {
		if(a == null || b == null) return null;
		AI_Game_Output result = new AI_Game_Output();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static AI_Game_Output sub(AI_Game_Output a, AI_Game_Output b, uint start, uint end) {
		if(a == null || b == null) return null;
		AI_Game_Output result = new AI_Game_Output();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(AI_Game_Output a, AI_Game_Output b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static AI_Game_Output max(AI_Game_Output a, AI_Game_Output b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static AI_Game_Output json(AI_Game_Output a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static AI_Game_Output setProperty(AI_Game_Output a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<AI_Game_Output> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/AI_Game_Output.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("AI_Game_Output的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<AI_Game_Output> list = new List<AI_Game_Output>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			AI_Game_Output obj = new AI_Game_Output();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.systemPrompt= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.generateTemplate= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.templateDescription= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.ifMarkdown= br.ReadBoolean();
			obj.jsonModle= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.ifJson= br.ReadBoolean();
			obj.ifSse= br.ReadBoolean();
			obj.step= br.ReadInt32();
			obj.ifParallel= br.ReadBoolean();
			obj.priority= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}
        private static ORMTables _ormDatabase;
        private static ORMTables ormDatabase
        {
            get
            {
                if (_ormDatabase == null)
                {
                    _ormDatabase = new ORMTables();

                }
                return _ormDatabase;

            }
        }
        public static AI_Game_Output getDataById(string id)
        {
            try
            {
                ORMTables db = ormDatabase;
                AI_Game_Output data = null;
                //lock (lockObj)
                //{
                data = db.AI_Game_Outputs.FirstOrDefault(d => d.id == id);
                if (data != null)
                {
                    return data;
                }
                else
                {
                    Console.WriteLine($"数据库 DesignAIChat_Output 表中 未找到 id:{id}");
                    return null;
                }

                //}
            }
            catch (Exception e)
            {
                Console.WriteLine(e.ToString());
                return null;
            }
        }
        public static List<AI_Game_Output> getAllDatasList()
        {
            try
            {
                List<AI_Game_Output> datas = null;
                ORMTables db = ormDatabase;
                try
                {
                    datas = db.AI_Game_Outputs.ToList();
                    if (datas == null)
                    {
                        return null;
                    }
                    return datas;

                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                    return null;
                }

            }
            catch (Exception e)
            {

                Console.WriteLine(e.ToString());
                return null;
            }
        }
    }


/// <summary>
/// AI_Game_OutputData
/// </summary>
[Table("ai_game_outputdatas")]
public class AI_Game_OutputData
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// id
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 系统prompt
	/// </summary>
	public string systemPrompt
	{
		get;set;
	}

	/// <summary>
	/// 用户prompt
	/// </summary>
	public string userPrompt
	{
		get;set;
	}

	/// <summary>
	/// 游戏基础数据
	/// </summary>
	public string gameBase
	{
		get;set;
	}

	/// <summary>
	/// 游戏基础数据json
	/// </summary>
	public string gameBaseJson
	{
		get;set;
	}

	/// <summary>
	/// 世界观和游戏目标
	/// </summary>
	public string worldViewAndTarget
	{
		get;set;
	}

	/// <summary>
	/// 世界观和游戏目标json
	/// </summary>
	public string wolrdViewAndTargetJson
	{
		get;set;
	}

	/// <summary>
	/// ign链接json
	/// </summary>
	public string ignJson
	{
		get;set;
	}

	/// <summary>
	/// 游戏简介
	/// </summary>
	public string gameIntroduce
	{
		get;set;
	}

	/// <summary>
	/// 游戏简介json
	/// </summary>
	public string gameIntriduceJson
	{
		get;set;
	}

	/// <summary>
	/// 章节
	/// </summary>
	public string chapters
	{
		get;set;
	}

	/// <summary>
	/// 章节json
	/// </summary>
	public string chapyersJson
	{
		get;set;
	}

	/// <summary>
	/// 角色
	/// </summary>
	public string characters
	{
		get;set;
	}

	/// <summary>
	/// 角色json
	/// </summary>
	public string charactersJson
	{
		get;set;
	}

	/// <summary>
	/// 场景
	/// </summary>
	public string scences
	{
		get;set;
	}

	/// <summary>
	/// 场景json
	/// </summary>
	public string scencesJson
	{
		get;set;
	}

	/// <summary>
	/// 天空盒关键词
	/// </summary>
	public string skyboxKeysJson
	{
		get;set;
	}

	/// <summary>
	/// 场景关键词
	/// </summary>
	public string  scencesKeysJson
	{
		get;set;
	}

	/// <summary>
	/// 角色关键词
	/// </summary>
	public string charactersKeysJson
	{
		get;set;
	}

	/// <summary>
	/// 场景音乐
	/// </summary>
	public string scencesMusic
	{
		get;set;
	}

	/// <summary>
	/// 场景音乐关键词
	/// </summary>
	public string scencesMusicKeys
	{
		get;set;
	}

	/// <summary>
	/// 场景音效
	/// </summary>
	public string scencesEffect
	{
		get;set;
	}

	/// <summary>
	/// 场景音效关键词
	/// </summary>
	public string scencesEffectKeys
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/AI_Game_OutputData.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			systemPrompt = (string)Convert.ChangeType(raw_data[i][1].ToString(), typeof(string));
		if(raw_data[i][2].ToString() != "")
			userPrompt = (string)Convert.ChangeType(raw_data[i][2].ToString(), typeof(string));
		if(raw_data[i][3].ToString() != "")
			gameBase = (string)Convert.ChangeType(raw_data[i][3].ToString(), typeof(string));
		if(raw_data[i][4].ToString() != "")
			gameBaseJson = (string)Convert.ChangeType(raw_data[i][4].ToString(), typeof(string));
		if(raw_data[i][5].ToString() != "")
			worldViewAndTarget = (string)Convert.ChangeType(raw_data[i][5].ToString(), typeof(string));
		if(raw_data[i][6].ToString() != "")
			wolrdViewAndTargetJson = (string)Convert.ChangeType(raw_data[i][6].ToString(), typeof(string));
		if(raw_data[i][7].ToString() != "")
			ignJson = (string)Convert.ChangeType(raw_data[i][7].ToString(), typeof(string));
		if(raw_data[i][8].ToString() != "")
			gameIntroduce = (string)Convert.ChangeType(raw_data[i][8].ToString(), typeof(string));
		if(raw_data[i][9].ToString() != "")
			gameIntriduceJson = (string)Convert.ChangeType(raw_data[i][9].ToString(), typeof(string));
		if(raw_data[i][10].ToString() != "")
			chapters = (string)Convert.ChangeType(raw_data[i][10].ToString(), typeof(string));
		if(raw_data[i][11].ToString() != "")
			chapyersJson = (string)Convert.ChangeType(raw_data[i][11].ToString(), typeof(string));
		if(raw_data[i][12].ToString() != "")
			characters = (string)Convert.ChangeType(raw_data[i][12].ToString(), typeof(string));
		if(raw_data[i][13].ToString() != "")
			charactersJson = (string)Convert.ChangeType(raw_data[i][13].ToString(), typeof(string));
		if(raw_data[i][14].ToString() != "")
			scences = (string)Convert.ChangeType(raw_data[i][14].ToString(), typeof(string));
		if(raw_data[i][15].ToString() != "")
			scencesJson = (string)Convert.ChangeType(raw_data[i][15].ToString(), typeof(string));
		if(raw_data[i][16].ToString() != "")
			skyboxKeysJson = (string)Convert.ChangeType(raw_data[i][16].ToString(), typeof(string));
		if(raw_data[i][17].ToString() != "")
			scencesKeysJson = (string )Convert.ChangeType(raw_data[i][17].ToString(), typeof(string ));
		if(raw_data[i][18].ToString() != "")
			charactersKeysJson = (string)Convert.ChangeType(raw_data[i][18].ToString(), typeof(string));
		if(raw_data[i][19].ToString() != "")
			scencesMusic = (string)Convert.ChangeType(raw_data[i][19].ToString(), typeof(string));
		if(raw_data[i][20].ToString() != "")
			scencesMusicKeys = (string)Convert.ChangeType(raw_data[i][20].ToString(), typeof(string));
		if(raw_data[i][21].ToString() != "")
			scencesEffect = (string)Convert.ChangeType(raw_data[i][21].ToString(), typeof(string));
		if(raw_data[i][22].ToString() != "")
			scencesEffectKeys = (string)Convert.ChangeType(raw_data[i][22].ToString(), typeof(string));
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"systemPrompt", "string"},
		{"userPrompt", "string"},
		{"gameBase", "string"},
		{"gameBaseJson", "string"},
		{"worldViewAndTarget", "string"},
		{"wolrdViewAndTargetJson", "string"},
		{"ignJson", "string"},
		{"gameIntroduce", "string"},
		{"gameIntriduceJson", "string"},
		{"chapters", "string"},
		{"chapyersJson", "string"},
		{"characters", "string"},
		{"charactersJson", "string"},
		{"scences", "string"},
		{"scencesJson", "string"},
		{"skyboxKeysJson", "string"},
		{"scencesKeysJson", "string "},
		{"charactersKeysJson", "string"},
		{"scencesMusic", "string"},
		{"scencesMusicKeys", "string"},
		{"scencesEffect", "string"},
		{"scencesEffectKeys", "string"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "systemPrompt=" + systemPrompt + "userPrompt=" + userPrompt + "gameBase=" + gameBase + "gameBaseJson=" + gameBaseJson + "worldViewAndTarget=" + worldViewAndTarget + "wolrdViewAndTargetJson=" + wolrdViewAndTargetJson + "ignJson=" + ignJson + "gameIntroduce=" + gameIntroduce + "gameIntriduceJson=" + gameIntriduceJson + "chapters=" + chapters + "chapyersJson=" + chapyersJson + "characters=" + characters + "charactersJson=" + charactersJson + "scences=" + scences + "scencesJson=" + scencesJson + "skyboxKeysJson=" + skyboxKeysJson + "scencesKeysJson=" + scencesKeysJson + "charactersKeysJson=" + charactersKeysJson + "scencesMusic=" + scencesMusic + "scencesMusicKeys=" + scencesMusicKeys + "scencesEffect=" + scencesEffect + "scencesEffectKeys=" + scencesEffectKeys;
	}
	public static string[] propList = { "id","systemPrompt","userPrompt","gameBase","gameBaseJson","worldViewAndTarget","wolrdViewAndTargetJson","ignJson","gameIntroduce","gameIntriduceJson","chapters","chapyersJson","characters","charactersJson","scences","scencesJson","skyboxKeysJson","scencesKeysJson","charactersKeysJson","scencesMusic","scencesMusicKeys","scencesEffect","scencesEffectKeys", };
	public static AI_Game_OutputData add(AI_Game_OutputData a, AI_Game_OutputData b, uint start, uint end, AI_Game_OutputData limit = null) {
		if(a == null || b == null) return null;
		AI_Game_OutputData result = new AI_Game_OutputData();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static AI_Game_OutputData sub(AI_Game_OutputData a, AI_Game_OutputData b, uint start, uint end) {
		if(a == null || b == null) return null;
		AI_Game_OutputData result = new AI_Game_OutputData();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(AI_Game_OutputData a, AI_Game_OutputData b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static AI_Game_OutputData max(AI_Game_OutputData a, AI_Game_OutputData b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static AI_Game_OutputData json(AI_Game_OutputData a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static AI_Game_OutputData setProperty(AI_Game_OutputData a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<AI_Game_OutputData> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/AI_Game_OutputData.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("AI_Game_OutputData的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<AI_Game_OutputData> list = new List<AI_Game_OutputData>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			AI_Game_OutputData obj = new AI_Game_OutputData();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.systemPrompt= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.userPrompt= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.gameBase= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.gameBaseJson= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.worldViewAndTarget= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.wolrdViewAndTargetJson= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.ignJson= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.gameIntroduce= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.gameIntriduceJson= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.chapters= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.chapyersJson= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.characters= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.charactersJson= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.scences= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.scencesJson= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.skyboxKeysJson= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.charactersKeysJson= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.scencesMusic= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.scencesMusicKeys= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.scencesEffect= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.scencesEffectKeys= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}
        private static ORMTables _ormDatabase;
        private static ORMTables ormDatabase
        {
            get
            {
                if (_ormDatabase == null)
                {
                    _ormDatabase = new ORMTables();

                }
                return _ormDatabase;

            }
        }
        public static AI_Game_OutputData getDataById(string id)
        {
            try
            {
                ORMTables db = ormDatabase;
                AI_Game_OutputData data = null;
                //lock (lockObj)
                //{
                data = db.AI_Game_OutputDatas.FirstOrDefault(d => d.id == id);
                if (data != null)
                {
                    return data;
                }
                else
                {
                    Console.WriteLine($"数据库 DesignAIChat_Output 表中 未找到 id:{id}");
                    return null;
                }

                //}
            }
            catch (Exception e)
            {
                Console.WriteLine(e.ToString());
                return null;
            }
        }
        public static List<AI_Game_OutputData> getAllDatasList()
        {
            try
            {
                List<AI_Game_OutputData> datas = null;
                ORMTables db = ormDatabase;
                try
                {
                    datas = db.AI_Game_OutputDatas.ToList();
                    if (datas == null)
                    {
                        return null;
                    }
                    return datas;

                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                    return null;
                }

            }
            catch (Exception e)
            {

                Console.WriteLine(e.ToString());
                return null;
            }
        }
    }


/// <summary>
/// ArtAIChat
/// </summary>
[Table("artaichat_outputs")]
public class ArtAIChat_Output
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// id
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 设置前提
	/// </summary>
	public string systemPrompt
	{
		get;set;
	}

	/// <summary>
	/// 生成模板
	/// </summary>
	public string generateTemplate
	{
		get;set;
	}

	/// <summary>
	/// 模板描述
	/// </summary>
	public string templateDescription
	{
		get;set;
	}

	/// <summary>
	/// 是否输出markdown
	/// </summary>
	public bool ifMarkdown
	{
		get;set;
	}

	/// <summary>
	/// json输出模板
	/// </summary>
	public string jsonModle
	{
		get;set;
	}

	/// <summary>
	/// 是否输出json
	/// </summary>
	public bool ifJson
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/ArtAIChat.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			systemPrompt = (string)Convert.ChangeType(raw_data[i][1].ToString(), typeof(string));
		if(raw_data[i][2].ToString() != "")
			generateTemplate = (string)Convert.ChangeType(raw_data[i][2].ToString(), typeof(string));
		if(raw_data[i][3].ToString() != "")
			templateDescription = (string)Convert.ChangeType(raw_data[i][3].ToString(), typeof(string));
		if(raw_data[i][4].ToString() != "")
			ifMarkdown = (bool)Convert.ChangeType(raw_data[i][4].ToString(), typeof(bool));
		if(raw_data[i][5].ToString() != "")
			jsonModle = (string)Convert.ChangeType(raw_data[i][5].ToString(), typeof(string));
		if(raw_data[i][6].ToString() != "")
			ifJson = (bool)Convert.ChangeType(raw_data[i][6].ToString(), typeof(bool));
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"systemPrompt", "string"},
		{"generateTemplate", "string"},
		{"templateDescription", "string"},
		{"ifMarkdown", "bool"},
		{"jsonModle", "string"},
		{"ifJson", "bool"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "systemPrompt=" + systemPrompt + "generateTemplate=" + generateTemplate + "templateDescription=" + templateDescription + "ifMarkdown=" + ifMarkdown + "jsonModle=" + jsonModle + "ifJson=" + ifJson;
	}
	public static string[] propList = { "id","systemPrompt","generateTemplate","templateDescription","ifMarkdown","jsonModle","ifJson", };
	public static ArtAIChat_Output add(ArtAIChat_Output a, ArtAIChat_Output b, uint start, uint end, ArtAIChat_Output limit = null) {
		if(a == null || b == null) return null;
		ArtAIChat_Output result = new ArtAIChat_Output();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static ArtAIChat_Output sub(ArtAIChat_Output a, ArtAIChat_Output b, uint start, uint end) {
		if(a == null || b == null) return null;
		ArtAIChat_Output result = new ArtAIChat_Output();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(ArtAIChat_Output a, ArtAIChat_Output b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static ArtAIChat_Output max(ArtAIChat_Output a, ArtAIChat_Output b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static ArtAIChat_Output json(ArtAIChat_Output a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static ArtAIChat_Output setProperty(ArtAIChat_Output a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<ArtAIChat_Output> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/ArtAIChat_Output.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("ArtAIChat_Output的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<ArtAIChat_Output> list = new List<ArtAIChat_Output>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			ArtAIChat_Output obj = new ArtAIChat_Output();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.systemPrompt= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.generateTemplate= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.templateDescription= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.ifMarkdown= br.ReadBoolean();
			obj.jsonModle= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.ifJson= br.ReadBoolean();
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}
        private static ORMTables _ormDatabase;
        private static ORMTables ormDatabase
        {
            get
            {
                if (_ormDatabase == null)
                {
                    _ormDatabase = new ORMTables();

                }
                return _ormDatabase;

            }
        }
        public static ArtAIChat_Output getDataById(string id)
        {
            try
            {
                ORMTables db = ormDatabase;
                ArtAIChat_Output data = null;
                //lock (lockObj)
                //{
                data = db.ArtAIChat_Outputs.FirstOrDefault(d => d.id == id);
                if (data != null)
                {
                    return data;
                }
                else
                {
                    Console.WriteLine($"数据库 DesignAIChat_Output 表中 未找到 id:{id}");
                    return null;
                }

                //}
            }
            catch (Exception e)
            {
                Console.WriteLine(e.ToString());
                return null;
            }
        }
        public static List<ArtAIChat_Output> getAllDatasList()
        {
            try
            {
                List<ArtAIChat_Output> datas = null;
                ORMTables db = ormDatabase;
                try
                {
                    datas = db.ArtAIChat_Outputs.ToList();
                    if (datas == null)
                    {
                        return null;
                    }
                    return datas;

                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                    return null;
                }

            }
            catch (Exception e)
            {

                Console.WriteLine(e.ToString());
                return null;
            }
        }

    }


    /// <summary>
    /// DesignAIChat
    /// </summary>
    [Table("designaichat_outputs")]
public class DesignAIChat_Output
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// id
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 设置前提
	/// </summary>
	public string systemPrompt
	{
		get;set;
	}

	/// <summary>
	/// 生成模板
	/// </summary>
	public string generateTemplate
	{
		get;set;
	}

	/// <summary>
	/// 模板描述
	/// </summary>
	public string templateDescription
	{
		get;set;
	}

	/// <summary>
	/// 是否输出markdown
	/// </summary>
	public bool ifMarkdown
	{
		get;set;
	}

	/// <summary>
	/// json输出模板
	/// </summary>
	public string jsonModle
	{
		get;set;
	}

	/// <summary>
	/// 是否输出json
	/// </summary>
	public bool ifJson
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/DesignAIChat.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			systemPrompt = (string)Convert.ChangeType(raw_data[i][1].ToString(), typeof(string));
		if(raw_data[i][2].ToString() != "")
			generateTemplate = (string)Convert.ChangeType(raw_data[i][2].ToString(), typeof(string));
		if(raw_data[i][3].ToString() != "")
			templateDescription = (string)Convert.ChangeType(raw_data[i][3].ToString(), typeof(string));
		if(raw_data[i][4].ToString() != "")
			ifMarkdown = (bool)Convert.ChangeType(raw_data[i][4].ToString(), typeof(bool));
		if(raw_data[i][5].ToString() != "")
			jsonModle = (string)Convert.ChangeType(raw_data[i][5].ToString(), typeof(string));
		if(raw_data[i][6].ToString() != "")
			ifJson = (bool)Convert.ChangeType(raw_data[i][6].ToString(), typeof(bool));
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"systemPrompt", "string"},
		{"generateTemplate", "string"},
		{"templateDescription", "string"},
		{"ifMarkdown", "bool"},
		{"jsonModle", "string"},
		{"ifJson", "bool"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "systemPrompt=" + systemPrompt + "generateTemplate=" + generateTemplate + "templateDescription=" + templateDescription + "ifMarkdown=" + ifMarkdown + "jsonModle=" + jsonModle + "ifJson=" + ifJson;
	}
	public static string[] propList = { "id","systemPrompt","generateTemplate","templateDescription","ifMarkdown","jsonModle","ifJson", };
	public static DesignAIChat_Output add(DesignAIChat_Output a, DesignAIChat_Output b, uint start, uint end, DesignAIChat_Output limit = null) {
		if(a == null || b == null) return null;
		DesignAIChat_Output result = new DesignAIChat_Output();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static DesignAIChat_Output sub(DesignAIChat_Output a, DesignAIChat_Output b, uint start, uint end) {
		if(a == null || b == null) return null;
		DesignAIChat_Output result = new DesignAIChat_Output();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(DesignAIChat_Output a, DesignAIChat_Output b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static DesignAIChat_Output max(DesignAIChat_Output a, DesignAIChat_Output b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static DesignAIChat_Output json(DesignAIChat_Output a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static DesignAIChat_Output setProperty(DesignAIChat_Output a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<DesignAIChat_Output> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/DesignAIChat_Output.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("DesignAIChat_Output的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<DesignAIChat_Output> list = new List<DesignAIChat_Output>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			DesignAIChat_Output obj = new DesignAIChat_Output();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.systemPrompt= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.generateTemplate= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.templateDescription= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.ifMarkdown= br.ReadBoolean();
			obj.jsonModle= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.ifJson= br.ReadBoolean();
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}
        private static ORMTables _ormDatabase;
        private static ORMTables ormDatabase
        {
            get
            {
                if (_ormDatabase == null)
                {
                    _ormDatabase = new ORMTables();

                }
                return _ormDatabase;

            }
        }
        public static DesignAIChat_Output getDataById(string id)
        {
            try
            {
                ORMTables db = ormDatabase;
                DesignAIChat_Output data = null;
                //lock (lockObj)
                //{
                data = db.DesignAIChat_Outputs.FirstOrDefault(d => d.id == id);
                if (data != null)
                {
                    return data;
                }
                else
                {
                    Console.WriteLine($"数据库 DesignAIChat_Output 表中 未找到 id:{id}");
                    return null;
                }

                //}
            }
            catch (Exception e)
            {
                Console.WriteLine(e.ToString());
                return null;
            }
        }
        public static List<DesignAIChat_Output> getAllDatasList()
        {
            try
            {
                List<DesignAIChat_Output> datas = null;
                ORMTables db = ormDatabase;
                try
                {
                    datas = db.DesignAIChat_Outputs.ToList();
                    if (datas == null)
                    {
                        return null;
                    }
                    return datas;

                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.Message);
                    return null;
                }

            }
            catch (Exception e)
            {

                Console.WriteLine(e.ToString());
                return null;
            }
        }

    }


    /// <summary>
    /// ErrorInfo
    /// </summary>
    [Table("errorinfos")]
public class ErrorInfo
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// ID
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 错误消息
	/// </summary>
	public string message
	{
		get;set;
	}

	/// <summary>
	/// 异常类型
	/// </summary>
	public string errorType
	{
		get;set;
	}

	/// <summary>
	/// 创建时间
	/// </summary>
	public string time
	{
		get;set;
	}

	/// <summary>
	/// 机型
	/// </summary>
	public string modelType
	{
		get;set;
	}

	/// <summary>
	/// IP地址
	/// </summary>
	public string ip
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/ErrorInfo.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			message = (string)Convert.ChangeType(raw_data[i][1].ToString(), typeof(string));
		if(raw_data[i][2].ToString() != "")
			errorType = (string)Convert.ChangeType(raw_data[i][2].ToString(), typeof(string));
		if(raw_data[i][3].ToString() != "")
			time = (string)Convert.ChangeType(raw_data[i][3].ToString(), typeof(string));
		if(raw_data[i][4].ToString() != "")
			modelType = (string)Convert.ChangeType(raw_data[i][4].ToString(), typeof(string));
		if(raw_data[i][5].ToString() != "")
			ip = (string)Convert.ChangeType(raw_data[i][5].ToString(), typeof(string));
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"message", "string"},
		{"errorType", "string"},
		{"time", "string"},
		{"modelType", "string"},
		{"ip", "string"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "message=" + message + "errorType=" + errorType + "time=" + time + "modelType=" + modelType + "ip=" + ip;
	}
	public static string[] propList = { "id","message","errorType","time","modelType","ip", };
	public static ErrorInfo add(ErrorInfo a, ErrorInfo b, uint start, uint end, ErrorInfo limit = null) {
		if(a == null || b == null) return null;
		ErrorInfo result = new ErrorInfo();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static ErrorInfo sub(ErrorInfo a, ErrorInfo b, uint start, uint end) {
		if(a == null || b == null) return null;
		ErrorInfo result = new ErrorInfo();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(ErrorInfo a, ErrorInfo b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static ErrorInfo max(ErrorInfo a, ErrorInfo b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static ErrorInfo json(ErrorInfo a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static ErrorInfo setProperty(ErrorInfo a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<ErrorInfo> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/ErrorInfo.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("ErrorInfo的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<ErrorInfo> list = new List<ErrorInfo>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			ErrorInfo obj = new ErrorInfo();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.message= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.errorType= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.time= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.modelType= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.ip= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}
}


/// <summary>
/// serverVar
/// </summary>
[Table("servervars")]
public class serverVar
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// 变量名
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 变量类型
	/// </summary>
	public string type
	{
		get;set;
	}

	/// <summary>
	/// 变量值
	/// </summary>
	public string value
	{
		get;set;
	}

	/// <summary>
	/// 保存时间类型
	/// </summary>
	public byte timeType
	{
		get;set;
	}

	/// <summary>
	/// 时间值
	/// </summary>
	public int timeValue
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/serverVar.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			type = (string)Convert.ChangeType(raw_data[i][1].ToString(), typeof(string));
		if(raw_data[i][2].ToString() != "")
			value = (string)Convert.ChangeType(raw_data[i][2].ToString(), typeof(string));
		if(raw_data[i][3].ToString() != "")
			timeType = (byte)Convert.ChangeType(raw_data[i][3].ToString(), typeof(byte));
		if(raw_data[i][4].ToString() != "")
			timeValue = (int)Convert.ChangeType(raw_data[i][4].ToString(), typeof(int));
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"type", "string"},
		{"value", "string"},
		{"timeType", "byte"},
		{"timeValue", "int"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "type=" + type + "value=" + value + "timeType=" + timeType + "timeValue=" + timeValue;
	}
	public static string[] propList = { "id","type","value","timeType","timeValue", };
	public static serverVar add(serverVar a, serverVar b, uint start, uint end, serverVar limit = null) {
		if(a == null || b == null) return null;
		serverVar result = new serverVar();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static serverVar sub(serverVar a, serverVar b, uint start, uint end) {
		if(a == null || b == null) return null;
		serverVar result = new serverVar();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(serverVar a, serverVar b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static serverVar max(serverVar a, serverVar b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static serverVar json(serverVar a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static serverVar setProperty(serverVar a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<serverVar> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/serverVar.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("serverVar的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<serverVar> list = new List<serverVar>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			serverVar obj = new serverVar();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.type= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.value= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.timeType= br.ReadByte();
			obj.timeValue= br.ReadInt32();
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}
}


/// <summary>
/// SeverConfig
/// </summary>
[Table("severconfigbases")]
public class SeverConfigBase
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// 配置ID
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 备注
	/// </summary>
	public string depict
	{
		get;set;
	}

	/// <summary>
	/// ip
	/// </summary>
	public string ip
	{
		get;set;
	}

	/// <summary>
	/// 网关地址
	/// </summary>
	public string gateWay
	{
		get;set;
	}

	/// <summary>
	/// 区服ID
	/// </summary>
	public string serverID
	{
		get;set;
	}

	/// <summary>
	/// 区服名称
	/// </summary>
	public string serverName
	{
		get;set;
	}

	/// <summary>
	/// 	/// 区服状态:
	/// 1.爆满
	/// 2.维护
	/// 3.流畅

	/// </summary>
	public byte serverState
	{
		get;set;
	}

	/// <summary>
	/// 新区
	/// </summary>
	public bool newServer
	{
		get;set;
	}

	/// <summary>
	/// MAC地址
	/// </summary>
	public string MAC
	{
		get;set;
	}

	/// <summary>
	/// 启动时间
	/// </summary>
	public long setupTime
	{
		get;set;
	}

	/// <summary>
	/// 状态
	/// </summary>
	public byte status
	{
		get;set;
	}

	/// <summary>
	/// 当前人数
	/// </summary>
	public uint playerSum
	{
		get;set;
	}

	/// <summary>
	/// 是否为网关
	/// </summary>
	public bool isGate
	{
		get;set;
	}

	/// <summary>
	/// 备注IP
	/// </summary>
	public string descIP
	{
		get;set;
	}

	/// <summary>
	/// 地图更新序号
	/// </summary>
	public ulong mapSaveVer
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/SeverConfig.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			depict = (string)Convert.ChangeType(raw_data[i][1].ToString(), typeof(string));
		if(raw_data[i][2].ToString() != "")
			ip = (string)Convert.ChangeType(raw_data[i][2].ToString(), typeof(string));
		if(raw_data[i][3].ToString() != "")
			gateWay = (string)Convert.ChangeType(raw_data[i][3].ToString(), typeof(string));
		if(raw_data[i][4].ToString() != "")
			serverID = (string)Convert.ChangeType(raw_data[i][4].ToString(), typeof(string));
		if(raw_data[i][5].ToString() != "")
			serverName = (string)Convert.ChangeType(raw_data[i][5].ToString(), typeof(string));
		if(raw_data[i][6].ToString() != "")
			serverState = (byte)Convert.ChangeType(raw_data[i][6].ToString(), typeof(byte));
		if(raw_data[i][7].ToString() != "")
			newServer = (bool)Convert.ChangeType(raw_data[i][7].ToString(), typeof(bool));
		if(raw_data[i][8].ToString() != "")
			MAC = (string)Convert.ChangeType(raw_data[i][8].ToString(), typeof(string));
		if(raw_data[i][9].ToString() != "")
			setupTime = (long)Convert.ChangeType(raw_data[i][9].ToString(), typeof(long));
		if(raw_data[i][10].ToString() != "")
			status = (byte)Convert.ChangeType(raw_data[i][10].ToString(), typeof(byte));
		if(raw_data[i][11].ToString() != "")
			playerSum = (uint)Convert.ChangeType(raw_data[i][11].ToString(), typeof(uint));
		if(raw_data[i][12].ToString() != "")
			isGate = (bool)Convert.ChangeType(raw_data[i][12].ToString(), typeof(bool));
		if(raw_data[i][13].ToString() != "")
			descIP = (string)Convert.ChangeType(raw_data[i][13].ToString(), typeof(string));
		if(raw_data[i][14].ToString() != "")
			mapSaveVer = (ulong)Convert.ChangeType(raw_data[i][14].ToString(), typeof(ulong));
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"depict", "string"},
		{"ip", "string"},
		{"gateWay", "string"},
		{"serverID", "string"},
		{"serverName", "string"},
		{"serverState", "byte"},
		{"newServer", "bool"},
		{"MAC", "string"},
		{"setupTime", "long"},
		{"status", "byte"},
		{"playerSum", "uint"},
		{"isGate", "bool"},
		{"descIP", "string"},
		{"mapSaveVer", "ulong"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "depict=" + depict + "ip=" + ip + "gateWay=" + gateWay + "serverID=" + serverID + "serverName=" + serverName + "serverState=" + serverState + "newServer=" + newServer + "MAC=" + MAC + "setupTime=" + setupTime + "status=" + status + "playerSum=" + playerSum + "isGate=" + isGate + "descIP=" + descIP + "mapSaveVer=" + mapSaveVer;
	}
	public static string[] propList = { "id","depict","ip","gateWay","serverID","serverName","serverState","newServer","MAC","setupTime","status","playerSum","isGate","descIP","mapSaveVer", };
	public static SeverConfigBase add(SeverConfigBase a, SeverConfigBase b, uint start, uint end, SeverConfigBase limit = null) {
		if(a == null || b == null) return null;
		SeverConfigBase result = new SeverConfigBase();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static SeverConfigBase sub(SeverConfigBase a, SeverConfigBase b, uint start, uint end) {
		if(a == null || b == null) return null;
		SeverConfigBase result = new SeverConfigBase();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(SeverConfigBase a, SeverConfigBase b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static SeverConfigBase max(SeverConfigBase a, SeverConfigBase b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static SeverConfigBase json(SeverConfigBase a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static SeverConfigBase setProperty(SeverConfigBase a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<SeverConfigBase> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/SeverConfigBase.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("SeverConfigBase的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<SeverConfigBase> list = new List<SeverConfigBase>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			SeverConfigBase obj = new SeverConfigBase();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.depict= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.ip= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.gateWay= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.serverID= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.serverName= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.serverState= br.ReadByte();
			obj.newServer= br.ReadBoolean();
			obj.MAC= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.setupTime= br.ReadInt64();
			obj.status= br.ReadByte();
			obj.playerSum= br.ReadUInt32();
			obj.isGate= br.ReadBoolean();
			obj.descIP= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.mapSaveVer= br.ReadUInt64();
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}
}


/// <summary>
/// SeverData
/// </summary>
[Table("severdatas")]
public class SeverData
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// 配置ID
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 	/// 区服状态:
	/// 1.爆满
	/// 2.维护
	/// 3.流畅

	/// </summary>
	public byte serverState
	{
		get;set;
	}

	/// <summary>
	/// 新区
	/// </summary>
	public bool newServer
	{
		get;set;
	}

	/// <summary>
	/// 启动时间
	/// </summary>
	public long setupTime
	{
		get;set;
	}

	/// <summary>
	/// 状态
	/// </summary>
	public byte status
	{
		get;set;
	}

	/// <summary>
	/// 当前人数
	/// </summary>
	public uint playerSum
	{
		get;set;
	}

	/// <summary>
	/// 开服时间
	/// </summary>
	public long openTime
	{
		get;set;
	}

	/// <summary>
	/// 服务器偏移时间
	/// </summary>
	public long addTime
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/SeverData.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			serverState = (byte)Convert.ChangeType(raw_data[i][1].ToString(), typeof(byte));
		if(raw_data[i][2].ToString() != "")
			newServer = (bool)Convert.ChangeType(raw_data[i][2].ToString(), typeof(bool));
		if(raw_data[i][3].ToString() != "")
			setupTime = (long)Convert.ChangeType(raw_data[i][3].ToString(), typeof(long));
		if(raw_data[i][4].ToString() != "")
			status = (byte)Convert.ChangeType(raw_data[i][4].ToString(), typeof(byte));
		if(raw_data[i][5].ToString() != "")
			playerSum = (uint)Convert.ChangeType(raw_data[i][5].ToString(), typeof(uint));
		if(raw_data[i][6].ToString() != "")
			openTime = (long)Convert.ChangeType(raw_data[i][6].ToString(), typeof(long));
		if(raw_data[i][7].ToString() != "")
			addTime = (long)Convert.ChangeType(raw_data[i][7].ToString(), typeof(long));
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"serverState", "byte"},
		{"newServer", "bool"},
		{"setupTime", "long"},
		{"status", "byte"},
		{"playerSum", "uint"},
		{"openTime", "long"},
		{"addTime", "long"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "serverState=" + serverState + "newServer=" + newServer + "setupTime=" + setupTime + "status=" + status + "playerSum=" + playerSum + "openTime=" + openTime + "addTime=" + addTime;
	}
	public static string[] propList = { "id","serverState","newServer","setupTime","status","playerSum","openTime","addTime", };
	public static SeverData add(SeverData a, SeverData b, uint start, uint end, SeverData limit = null) {
		if(a == null || b == null) return null;
		SeverData result = new SeverData();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static SeverData sub(SeverData a, SeverData b, uint start, uint end) {
		if(a == null || b == null) return null;
		SeverData result = new SeverData();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(SeverData a, SeverData b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static SeverData max(SeverData a, SeverData b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static SeverData json(SeverData a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static SeverData setProperty(SeverData a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<SeverData> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/SeverData.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("SeverData的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<SeverData> list = new List<SeverData>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			SeverData obj = new SeverData();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.serverState= br.ReadByte();
			obj.newServer= br.ReadBoolean();
			obj.setupTime= br.ReadInt64();
			obj.status= br.ReadByte();
			obj.playerSum= br.ReadUInt32();
			obj.openTime= br.ReadInt64();
			obj.addTime= br.ReadInt64();
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}
}


/// <summary>
/// SysActive
/// </summary>
[Table("sysactives")]
public class SysActive
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// 系统id
	/// </summary>
	public uint id
	{
		get;set;
	}

	/// <summary>
	/// 系统中文名字
	/// </summary>
	public string sysName_zh
	{
		get;set;
	}

	/// <summary>
	/// 系统英文名字
	/// </summary>
	public string sysName_en
	{
		get;set;
	}

	/// <summary>
	/// 激活方式
	/// </summary>
	public uint active
	{
		get;set;
	}

	/// <summary>
	/// 激活状态
	/// </summary>
	public uint activityStatus
	{
		get;set;
	}

	/// <summary>
	/// 加载方式
	/// </summary>
	public uint loadingMethod
	{
		get;set;
	}

	/// <summary>
	/// 启动数据统计
	/// </summary>
	public uint startDataTracing
	{
		get;set;
	}

	/// <summary>
	/// 系统模块
	/// </summary>
	public uint sysModule
	{
		get;set;
	}

	/// <summary>
	/// 模块版本号
	/// </summary>
	public uint moduleVersion
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/SysActive.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (uint)Convert.ChangeType(raw_data[i][0].ToString(), typeof(uint));
		if(raw_data[i][1].ToString() != "")
			sysName_zh = (string)Convert.ChangeType(raw_data[i][1].ToString(), typeof(string));
		if(raw_data[i][2].ToString() != "")
			sysName_en = (string)Convert.ChangeType(raw_data[i][2].ToString(), typeof(string));
		if(raw_data[i][3].ToString() != "")
			active = (uint)Convert.ChangeType(raw_data[i][3].ToString(), typeof(uint));
		if(raw_data[i][4].ToString() != "")
			activityStatus = (uint)Convert.ChangeType(raw_data[i][4].ToString(), typeof(uint));
		if(raw_data[i][5].ToString() != "")
			loadingMethod = (uint)Convert.ChangeType(raw_data[i][5].ToString(), typeof(uint));
		if(raw_data[i][6].ToString() != "")
			startDataTracing = (uint)Convert.ChangeType(raw_data[i][6].ToString(), typeof(uint));
		if(raw_data[i][7].ToString() != "")
			sysModule = (uint)Convert.ChangeType(raw_data[i][7].ToString(), typeof(uint));
		if(raw_data[i][8].ToString() != "")
			moduleVersion = (uint)Convert.ChangeType(raw_data[i][8].ToString(), typeof(uint));
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "uint"},
		{"sysName_zh", "string"},
		{"sysName_en", "string"},
		{"active", "uint"},
		{"activityStatus", "uint"},
		{"loadingMethod", "uint"},
		{"startDataTracing", "uint"},
		{"sysModule", "uint"},
		{"moduleVersion", "uint"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "sysName_zh=" + sysName_zh + "sysName_en=" + sysName_en + "active=" + active + "activityStatus=" + activityStatus + "loadingMethod=" + loadingMethod + "startDataTracing=" + startDataTracing + "sysModule=" + sysModule + "moduleVersion=" + moduleVersion;
	}
	public static string[] propList = { "id","sysName_zh","sysName_en","active","activityStatus","loadingMethod","startDataTracing","sysModule","moduleVersion", };
	public static SysActive add(SysActive a, SysActive b, uint start, uint end, SysActive limit = null) {
		if(a == null || b == null) return null;
		SysActive result = new SysActive();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static SysActive sub(SysActive a, SysActive b, uint start, uint end) {
		if(a == null || b == null) return null;
		SysActive result = new SysActive();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(SysActive a, SysActive b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static SysActive max(SysActive a, SysActive b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static SysActive json(SysActive a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static SysActive setProperty(SysActive a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<SysActive> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/SysActive.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("SysActive的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<SysActive> list = new List<SysActive>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			SysActive obj = new SysActive();
			obj.id= br.ReadUInt32();
			obj.sysName_zh= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.sysName_en= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.active= br.ReadUInt32();
			obj.activityStatus= br.ReadUInt32();
			obj.loadingMethod= br.ReadUInt32();
			obj.startDataTracing= br.ReadUInt32();
			obj.sysModule= br.ReadUInt32();
			obj.moduleVersion= br.ReadUInt32();
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}
}


/// <summary>
/// TimeEvent
/// </summary>
[Table("timeevents")]
public class TimeEvent
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// 配置ID
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 任务名称
	/// </summary>
	public string eventName
	{
		get;set;
	}

	/// <summary>
	/// 任务实际开始时间
	/// </summary>
	public ulong taskstartTime
	{
		get;set;
	}

	/// <summary>
	/// 任务结束时间
	/// </summary>
	public ulong taskEndTime
	{
		get;set;
	}

	/// <summary>
	/// 上次开始时间
	/// </summary>
	public ulong lastStartTime
	{
		get;set;
	}

	/// <summary>
	/// 上次结束时间
	/// </summary>
	public ulong lastEndTime
	{
		get;set;
	}

	/// <summary>
	/// 默认时间是0也就是utc时间
	/// </summary>
	public int serverTimeZone
	{
		get;set;
	}

	/// <summary>
	/// 重复循环次数，0就是无限次循环
	/// </summary>
	public int LoopCount
	{
		get;set;
	}

	/// <summary>
	/// 已经重复的次数
	/// </summary>
	public int LoopTimers
	{
		get;set;
	}

	/// <summary>
	/// 第一次是否有cd
	/// </summary>
	public bool isFristNoCD
	{
		get;set;
	}

	/// <summary>
	/// 任务间隔执行时间
	/// </summary>
	public ulong taskLoopTime
	{
		get;set;
	}

	/// <summary>
	/// 	/// 循环类型
	/// 1.日循环
	/// 2.周循环
	/// 3.月循环

	/// </summary>
	public byte timeType
	{
		get;set;
	}

	/// <summary>
	/// 每天任务开始的时间，和loopTime共同执行
	/// </summary>
	public ulong startTime
	{
		get;set;
	}

	/// <summary>
	/// 每天任务开始的时间的结束时间
	/// </summary>
	public ulong startLimitTime
	{
		get;set;
	}

	/// <summary>
	/// 前置任务id，可以组成任务集合
	/// </summary>
	public string predecessorTaskID
	{
		get;set;
	}

	/// <summary>
	/// 任务的回调事件名字
	/// </summary>
	public string taskEventString
	{
		get;set;
	}

	/// <summary>
	/// 任务执行日志列表
	/// </summary>
	public string taskEventLog
	{
		get;set;
	}

	/// <summary>
	/// 	/// 任务目前状态 
	/// 1正在执行，
	/// 2执行错误，
	/// 3执行成功
	/// 

	/// </summary>
	public int taskState
	{
		get;set;
	}

	/// <summary>
	/// 	/// 任务之前的执行状态
	/// 1正在执行，
	/// 2执行错误，
	/// 3执行成功，
	/// 注意写任务的一定要注意可能服务器被中断的情况

	/// </summary>
	public int taskPreviousState
	{
		get;set;
	}

	/// <summary>
	/// 	/// 繁忙时间段
	/// 如上次的执行有问题
	/// 在此时间段内不执行

	/// </summary>
	public string busyTime
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/TimeEvent.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			eventName = (string)Convert.ChangeType(raw_data[i][1].ToString(), typeof(string));
		if(raw_data[i][2].ToString() != "")
			taskstartTime = (ulong)Convert.ChangeType(raw_data[i][2].ToString(), typeof(ulong));
		if(raw_data[i][3].ToString() != "")
			taskEndTime = (ulong)Convert.ChangeType(raw_data[i][3].ToString(), typeof(ulong));
		if(raw_data[i][4].ToString() != "")
			lastStartTime = (ulong)Convert.ChangeType(raw_data[i][4].ToString(), typeof(ulong));
		if(raw_data[i][5].ToString() != "")
			lastEndTime = (ulong)Convert.ChangeType(raw_data[i][5].ToString(), typeof(ulong));
		if(raw_data[i][6].ToString() != "")
			serverTimeZone = (int)Convert.ChangeType(raw_data[i][6].ToString(), typeof(int));
		if(raw_data[i][7].ToString() != "")
			LoopCount = (int)Convert.ChangeType(raw_data[i][7].ToString(), typeof(int));
		if(raw_data[i][8].ToString() != "")
			LoopTimers = (int)Convert.ChangeType(raw_data[i][8].ToString(), typeof(int));
		if(raw_data[i][9].ToString() != "")
			isFristNoCD = (bool)Convert.ChangeType(raw_data[i][9].ToString(), typeof(bool));
		if(raw_data[i][10].ToString() != "")
			taskLoopTime = (ulong)Convert.ChangeType(raw_data[i][10].ToString(), typeof(ulong));
		if(raw_data[i][11].ToString() != "")
			timeType = (byte)Convert.ChangeType(raw_data[i][11].ToString(), typeof(byte));
		if(raw_data[i][12].ToString() != "")
			startTime = (ulong)Convert.ChangeType(raw_data[i][12].ToString(), typeof(ulong));
		if(raw_data[i][13].ToString() != "")
			startLimitTime = (ulong)Convert.ChangeType(raw_data[i][13].ToString(), typeof(ulong));
		if(raw_data[i][14].ToString() != "")
			predecessorTaskID = (string)Convert.ChangeType(raw_data[i][14].ToString(), typeof(string));
		if(raw_data[i][15].ToString() != "")
			taskEventString = (string)Convert.ChangeType(raw_data[i][15].ToString(), typeof(string));
		if(raw_data[i][16].ToString() != "")
			taskEventLog = (string)Convert.ChangeType(raw_data[i][16].ToString(), typeof(string));
		if(raw_data[i][17].ToString() != "")
			taskState = (int)Convert.ChangeType(raw_data[i][17].ToString(), typeof(int));
		if(raw_data[i][18].ToString() != "")
			taskPreviousState = (int)Convert.ChangeType(raw_data[i][18].ToString(), typeof(int));
		if(raw_data[i][19].ToString() != "")
			busyTime = (string)Convert.ChangeType(raw_data[i][19].ToString(), typeof(string));
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"eventName", "string"},
		{"taskstartTime", "ulong"},
		{"taskEndTime", "ulong"},
		{"lastStartTime", "ulong"},
		{"lastEndTime", "ulong"},
		{"serverTimeZone", "int"},
		{"LoopCount", "int"},
		{"LoopTimers", "int"},
		{"isFristNoCD", "bool"},
		{"taskLoopTime", "ulong"},
		{"timeType", "byte"},
		{"startTime", "ulong"},
		{"startLimitTime", "ulong"},
		{"predecessorTaskID", "string"},
		{"taskEventString", "string"},
		{"taskEventLog", "string"},
		{"taskState", "int"},
		{"taskPreviousState", "int"},
		{"busyTime", "string"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "eventName=" + eventName + "taskstartTime=" + taskstartTime + "taskEndTime=" + taskEndTime + "lastStartTime=" + lastStartTime + "lastEndTime=" + lastEndTime + "serverTimeZone=" + serverTimeZone + "LoopCount=" + LoopCount + "LoopTimers=" + LoopTimers + "isFristNoCD=" + isFristNoCD + "taskLoopTime=" + taskLoopTime + "timeType=" + timeType + "startTime=" + startTime + "startLimitTime=" + startLimitTime + "predecessorTaskID=" + predecessorTaskID + "taskEventString=" + taskEventString + "taskEventLog=" + taskEventLog + "taskState=" + taskState + "taskPreviousState=" + taskPreviousState + "busyTime=" + busyTime;
	}
	public static string[] propList = { "id","eventName","taskstartTime","taskEndTime","lastStartTime","lastEndTime","serverTimeZone","LoopCount","LoopTimers","isFristNoCD","taskLoopTime","timeType","startTime","startLimitTime","predecessorTaskID","taskEventString","taskEventLog","taskState","taskPreviousState","busyTime", };
	public static TimeEvent add(TimeEvent a, TimeEvent b, uint start, uint end, TimeEvent limit = null) {
		if(a == null || b == null) return null;
		TimeEvent result = new TimeEvent();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static TimeEvent sub(TimeEvent a, TimeEvent b, uint start, uint end) {
		if(a == null || b == null) return null;
		TimeEvent result = new TimeEvent();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(TimeEvent a, TimeEvent b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static TimeEvent max(TimeEvent a, TimeEvent b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static TimeEvent json(TimeEvent a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static TimeEvent setProperty(TimeEvent a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<TimeEvent> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/TimeEvent.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("TimeEvent的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<TimeEvent> list = new List<TimeEvent>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			TimeEvent obj = new TimeEvent();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.eventName= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.taskstartTime= br.ReadUInt64();
			obj.taskEndTime= br.ReadUInt64();
			obj.lastStartTime= br.ReadUInt64();
			obj.lastEndTime= br.ReadUInt64();
			obj.serverTimeZone= br.ReadInt32();
			obj.LoopCount= br.ReadInt32();
			obj.LoopTimers= br.ReadInt32();
			obj.isFristNoCD= br.ReadBoolean();
			obj.taskLoopTime= br.ReadUInt64();
			obj.timeType= br.ReadByte();
			obj.startTime= br.ReadUInt64();
			obj.startLimitTime= br.ReadUInt64();
			obj.predecessorTaskID= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.taskEventString= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.taskEventLog= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.taskState= br.ReadInt32();
			obj.taskPreviousState= br.ReadInt32();
			obj.busyTime= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}
}


/// <summary>
/// UserVar
/// </summary>
[Table("uservars")]
public class UserVar
{
	[Key]

/// <summary>
/// ORM Table ID
/// </summary>
public uint dbID 
	{
		get;set;
	}

	/// <summary>
	/// 用户uuid
	/// </summary>
	public string id
	{
		get;set;
	}

	/// <summary>
	/// 变量名
	/// </summary>
	public string name
	{
		get;set;
	}

	/// <summary>
	/// 变量类型
	/// </summary>
	public string type
	{
		get;set;
	}

	/// <summary>
	/// 变量值
	/// </summary>
	public string value
	{
		get;set;
	}

	/// <summary>
	/// 保存时间类型
	/// </summary>
	public byte timeType
	{
		get;set;
	}

	/// <summary>
	/// 时间值
	/// </summary>
	public int timeValue
	{
		get;set;
	}

	public static string getFileName()
	{
		return "baseExcel/UserVar.xlsx";
	}
	public void feed(DataRowCollection raw_data, int i)
	{
		if(raw_data[i][0].ToString() != "")
			id = (string)Convert.ChangeType(raw_data[i][0].ToString(), typeof(string));
		if(raw_data[i][1].ToString() != "")
			name = (string)Convert.ChangeType(raw_data[i][1].ToString(), typeof(string));
		if(raw_data[i][2].ToString() != "")
			type = (string)Convert.ChangeType(raw_data[i][2].ToString(), typeof(string));
		if(raw_data[i][3].ToString() != "")
			value = (string)Convert.ChangeType(raw_data[i][3].ToString(), typeof(string));
		if(raw_data[i][4].ToString() != "")
			timeType = (byte)Convert.ChangeType(raw_data[i][4].ToString(), typeof(byte));
		if(raw_data[i][5].ToString() != "")
			timeValue = (int)Convert.ChangeType(raw_data[i][5].ToString(), typeof(int));
	}
	public static Dictionary<string, string> typeList = new Dictionary<string, string>() {
		{"id", "string"},
		{"name", "string"},
		{"type", "string"},
		{"value", "string"},
		{"timeType", "byte"},
		{"timeValue", "int"},
	};
	public override string ToString()
	{
		return "" + "id=" + id + "name=" + name + "type=" + type + "value=" + value + "timeType=" + timeType + "timeValue=" + timeValue;
	}
	public static string[] propList = { "id","name","type","value","timeType","timeValue", };
	public static UserVar add(UserVar a, UserVar b, uint start, uint end, UserVar limit = null) {
		if(a == null || b == null) return null;
		UserVar result = new UserVar();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			var vr = (uint)va + (uint)vb;
			result.GetType().GetProperty(propList[i]).SetValue(result, vr);
			if(limit != null) {
				var vlimit = limit.GetType().GetProperty(propList[i]).GetValue(limit);
				if(vr > (uint)vlimit) {
					result.GetType().GetProperty(propList[i]).SetValue(result, vlimit);
				}
			}
		}
		return result;
	}

	public static UserVar sub(UserVar a, UserVar b, uint start, uint end) {
		if(a == null || b == null) return null;
		UserVar result = new UserVar();
		for(uint i = Math.Max(start, 0), e = Math.Min(end, (uint)propList.Length); i < e; i++) {
			var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
			var va = a.GetType().GetProperty(propList[i]).GetValue(a);
			result.GetType().GetProperty(propList[i]).SetValue(result, (uint)va - (uint)vb);
		}
		return result;
	}

	public static bool larger(UserVar a, UserVar b, uint i = 0) {
		return (uint)a.GetType().GetProperty(propList[i]).GetValue(a) > (uint)b.GetType().GetProperty(propList[i]).GetValue(b);
	}

	public static UserVar max(UserVar a, UserVar b, uint i = 0) {
		var vb = b.GetType().GetProperty(propList[i]).GetValue(b);
		var va = a.GetType().GetProperty(propList[i]).GetValue(a);
		if((uint)va > (uint)vb)
			return a;
		return b;
	}

	public static UserVar json(UserVar a, string data) {
		var d = JObject.Parse(data);
		foreach (JProperty p in d.Properties()) {
			if(p.Name == "dbID")
				continue;
			string vtype = typeList[p.Name];
			switch (vtype) {
				case "uint":
					a.GetType().GetProperty(p.Name).SetValue(a, UInt32.Parse(p.Value.ToString()));
					break;
				case "int":
					a.GetType().GetProperty(p.Name).SetValue(a, Int32.Parse(p.Value.ToString()));
					break;
				case "string":
					a.GetType().GetProperty(p.Name).SetValue(a, p.Value.ToString());
					break;
				case "byte":
					a.GetType().GetProperty(p.Name).SetValue(a, Byte.Parse(p.Value.ToString()));
					break;
				case "bool":
					a.GetType().GetProperty(p.Name).SetValue(a, Boolean.Parse(p.Value.ToString()));
					break;
			}
		}
		return a;
	}

	public static UserVar setProperty(UserVar a, uint p, dynamic value) {
		a.GetType().GetProperty(propList[p]).SetValue(a, value);
		return a;
	}

	public static List<UserVar> readAllData(){
		string excelJsonPath = AppContext.BaseDirectory + "excelData/UserVar.jason";

		if(!File.Exists(excelJsonPath)){
			Console.WriteLine("UserVar的二进制文件未找到");
			return null;
		}

		FileStream fs = File.OpenRead(excelJsonPath);
		BinaryReader br = new BinaryReader(fs);

		int configNum = br.ReadInt32();
		for (int i = 0; i < configNum; i++){
			byte[] keyArray = br.ReadBytes(br.ReadUInt16());

			byte[] valueArray = br.ReadBytes(br.ReadUInt16());
		}
		List<UserVar> list = new List<UserVar>();

		int row = br.ReadInt32();
		int col = br.ReadInt32();

		for (int i = 0; i < row; i++){
			UserVar obj = new UserVar();
			obj.id= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.name= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.type= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.value= System.Text.Encoding.UTF8.GetString(br.ReadBytes(br.ReadUInt16()));
			obj.timeType= br.ReadByte();
			obj.timeValue= br.ReadInt32();
			
			list.Add(obj);
		}

		if(br!=null){
			br.Dispose();
		}
		if(fs!=null){
			fs.Dispose();
		}
		return list;
	}
}

		/// <summary>
		/// 数据库里的表
		/// 这里的属性如果有修改需要清空数据库, 或者新建一个数据库 (数据库只要是空的就行)
		/// 如果只是表里的数据有修改, 只需要清空对应的表, 重启服务器会重新填数据
		/// </summary>
		public class ORMTables: DBBase
		{
		/// <summary>
		/// Account
		/// <summary>
		public DbSet<Account> Accounts { get; set; }
		/// <summary>
		/// AI_Game_Output
		/// <summary>
		public DbSet<AI_Game_Output> AI_Game_Outputs { get; set; }
		/// <summary>
		/// AI_Game_OutputData
		/// <summary>
		public DbSet<AI_Game_OutputData> AI_Game_OutputDatas { get; set; }
		/// <summary>
		/// ArtAIChat
		/// <summary>
		public DbSet<ArtAIChat_Output> ArtAIChat_Outputs { get; set; }
		/// <summary>
		/// DesignAIChat
		/// <summary>
		public DbSet<DesignAIChat_Output> DesignAIChat_Outputs { get; set; }
		/// <summary>
		/// ErrorInfo
		/// <summary>
		public DbSet<ErrorInfo> ErrorInfos { get; set; }
		/// <summary>
		/// serverVar
		/// <summary>
		public DbSet<serverVar> serverVars { get; set; }
		/// <summary>
		/// SeverConfig
		/// <summary>
		public DbSet<SeverConfigBase> SeverConfigBases { get; set; }
		/// <summary>
		/// SeverData
		/// <summary>
		public DbSet<SeverData> SeverDatas { get; set; }
		/// <summary>
		/// SysActive
		/// <summary>
		public DbSet<SysActive> SysActives { get; set; }
		/// <summary>
		/// TimeEvent
		/// <summary>
		public DbSet<TimeEvent> TimeEvents { get; set; }
		/// <summary>
		/// UserVar
		/// <summary>
		public DbSet<UserVar> UserVars { get; set; }
		public void readAllData(){
			ORMTables db = new ORMTables();
			db.Accounts.AddRange(Account.readAllData().ToArray());
			db.AI_Game_Outputs.AddRange(AI_Game_Output.readAllData().ToArray());
			db.AI_Game_OutputDatas.AddRange(AI_Game_OutputData.readAllData().ToArray());
			db.ArtAIChat_Outputs.AddRange(ArtAIChat_Output.readAllData().ToArray());
			db.DesignAIChat_Outputs.AddRange(DesignAIChat_Output.readAllData().ToArray());
			db.ErrorInfos.AddRange(ErrorInfo.readAllData().ToArray());
			db.serverVars.AddRange(serverVar.readAllData().ToArray());
			db.SeverConfigBases.AddRange(SeverConfigBase.readAllData().ToArray());
			db.SeverDatas.AddRange(SeverData.readAllData().ToArray());
			db.SysActives.AddRange(SysActive.readAllData().ToArray());
			db.TimeEvents.AddRange(TimeEvent.readAllData().ToArray());
			db.UserVars.AddRange(UserVar.readAllData().ToArray());
			db.SaveChanges();
			db.Dispose();
			Console.WriteLine("数据上传数据库完毕");
		}
		}
}
